import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/molecules/tab_bar_view.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';
import 'package:melodyze/modules/auth/bloc/authentication_event.dart';
import 'package:melodyze/modules/profile/bloc/cubit/profile_draft_selection_cubit.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/bloc/profile_state.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_final_recordings.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_draft_recordings.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_photo_edit_modal.dart';

@RoutePage()
class ProfileScreen extends StatefulWidget {
  final bool showCurrentRecording;
  const ProfileScreen({super.key, this.showCurrentRecording = false});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final ValueNotifier<int> currentIndex = ValueNotifier(0);

  final _draftSelectionCubit = ProfileDraftSelectionCubit();

  StreamSubscription<ProfileDraftSelectionState>? _subscription;
  bool _isRecordingSelected = false;

  @override
  void initState() {
    super.initState();
    if (widget.showCurrentRecording) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _draftSelectionCubit.onDraftRecordingSelected(DI().resolve<ProfileBloc>().recordings.first);
      });
    }
    _subscription = _draftSelectionCubit.stream.listen((state) {
      if (mounted) {
        setState(() {
          _isRecordingSelected = state.recording != null;
        });
      }
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  bool get isRecordingSelected => _isRecordingSelected;

  void _showProfilePhototureEditModal(BuildContext context, String? currentImageUrl, bool hasUserDp) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ProfilePhototureEditModal(
        currentImageUrl: currentImageUrl,
        hasUserDp: hasUserDp,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _draftSelectionCubit,
      child: BlocConsumer<ProfileBloc, BlocState>(
        listener: (context, state) {
          if (state is LogOutClickedState || state is AccountDeletedState) {
            context.read<AuthenticationBloc>().add(SignOut());
          }
          if (state is ProfileSnackBarState) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.message,
                  style: AppTextStyles.text16medium,
                ),
                duration: const Duration(seconds: 2),
                backgroundColor: state.isError ? Colors.red : Colors.green,
              ),
            );
          }
        },
        buildWhen: (previous, current) => current is! ProfileSnackBarState,
        builder: (context, state) {
          return MeloScaffold(
            showBackground: true,
            showBackButton: isRecordingSelected,
            onBackPressed: () {
              _draftSelectionCubit.onDraftRecordingUnselected();
            },
            onPopInvoked: () {
              if (_isRecordingSelected) {
                _draftSelectionCubit.onDraftRecordingUnselected();
              }
            },
            secondaryAction: (context) => PopupMenuButton<String>(
              icon: ShaderMask(
                shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
                child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
              ),
              onSelected: (value) async {
                await DI().resolve<ProfileBloc>().handleMenubarActions(context, value);
              },
              color: Colors.transparent,
              itemBuilder: (BuildContext context) => [
                PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: AppGradientContainer(
                    gradient: AppGradients.gradientBlackTeal,
                    child: Column(
                      children: [
                        PopupMenuItem(
                          value: 'logout',
                          child: ListTile(
                            leading: Icon(Icons.logout, color: Colors.white),
                            title: Text(
                              'Logout',
                              style: AppTextStyles.text20regular.copyWith(
                                fontFamily: AppFonts.iceland,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        // ImageLoader.fromAsset(AssetPaths.gradientdivider),
                        // PopupMenuItem(
                        //   value: 'clear_cache',
                        //   child: ListTile(
                        //     leading: Icon(
                        //       Icons.cached_outlined,
                        //       color: Colors.white,
                        //     ),
                        //     title: Text(
                        //       'Clear Cache',
                        //       style: AppTextStyles.text20regular.copyWith(
                        //         fontFamily: AppFonts.iceland,
                        //         color: Colors.white,
                        //       ),
                        //     ),
                        //   ),
                        // ),
                        ImageLoader.fromAsset(AssetPaths.gradientdivider),
                        PopupMenuItem(
                          value: 'delete_account',
                          child: ListTile(
                            leading: Icon(
                              Icons.delete_forever,
                              color: Colors.red,
                            ),
                            title: Text(
                              'Delete My Account',
                              style: AppTextStyles.text20regular.copyWith(
                                fontFamily: AppFonts.iceland,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            body: SafeArea(
              bottom: false,
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    BlocConsumer<ProfileBloc, BlocState>(
                        listener: (context, state) {
                          if (state is ProfileUpdatedState) {
                            context.read<ProfileBloc>().add(const LoadRecordingsEvent());
                          }
                        },
                        buildWhen: (previous, current) => current is ProfileUpdatedState || current is ProfilePhotoUploadingState,
                        builder: (context, profileState) {
                          return BlocBuilder<ProfileDraftSelectionCubit, ProfileDraftSelectionState>(
                            builder: (context, state) {
                              final user = context.read<ProfileBloc>().melodyzeUser;
                              final userProfileUrl = user.profilePicUrl.isNullOrEmpty ? null : '${user.profilePicUrl!}?t=${DateTime.now().millisecondsSinceEpoch}';
                              final image = state.recording?.thumbnailPath ?? userProfileUrl ?? Config.noUserDP;
                              final title = state.recording != null ? FileUtils.fromSnakeCase(state.recording?.title.split('-').first ?? '') : user.username;
                              return Column(
                                children: [
                                  Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      GestureDetector(
                                        onTap: () => _showProfilePhototureEditModal(
                                          context,
                                          image,
                                          !user.profilePicUrl.isNullOrEmpty,
                                        ),
                                        child: SizedBox(
                                          height: 100.0,
                                          width: 100.0,
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(100.0),
                                            child: profileState is ProfilePhotoUploadingState ? const AppCircularProgressIndicator(strokeWidth: 2) : ImageLoader.network(image),
                                          ),
                                        ),
                                      ),
                                      if (state.recording == null) // Only show edit button for user profile
                                        Positioned(
                                          right: 0,
                                          top: 0,
                                          child: GestureDetector(
                                            onTap: () => _showProfilePhototureEditModal(
                                              context,
                                              image,
                                              !user.profilePicUrl.isNullOrEmpty,
                                            ),
                                            child: Container(
                                              width: 28,
                                              height: 28,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: AppColors.greySlider,
                                                  width: 0.5,
                                                ),
                                              ),
                                              child: const Icon(
                                                Icons.edit_outlined,
                                                color: AppColors.greySlider,
                                                size: 18,
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 16.0),
                                  Text(
                                    title,
                                    style: AppTextStyles.headline4.copyWith(
                                      fontFamily: AppFonts.inter,
                                    ),
                                  ),
                                  const SizedBox(height: 8.0),
                                ],
                              );
                            },
                          );
                        }),
                    Expanded(
                      child: TabBars(
                        initialIndex: 0,
                        values: const ['Drafts', 'Published'],
                        selectedItemColor: AppColors.brightPurple,
                        onTap: (index) {
                          currentIndex.value = index;
                        },
                        child: BlocBuilder<ProfileBloc, BlocState>(
                          buildWhen: (previous, current) => current is! ProfileSnackBarState,
                          builder: (context, state) {
                            if (state is LoadingState) {
                              return const Expanded(
                                child: Column(
                                  children: [
                                    Spacer(),
                                    AppCircularProgressIndicator(),
                                    Spacer(),
                                  ],
                                ),
                              );
                            }
                            if (state is BlocSuccessState<ProfileRecordingsUiModel>) {
                              return Expanded(
                                child: ValueListenableBuilder(
                                  valueListenable: currentIndex,
                                  builder: (context, value, child) {
                                    if (value == 0) {
                                      return ProfileDraftRecordings(
                                        showCurrentRecording: state.data.showCurrentRecording,
                                        recordings: state.data.draftRecordings,
                                      );
                                    } else {
                                      return ProfileFinalRecordings(
                                        showCurrentRecording: false,
                                        recordings: state.data.finalRecordings,
                                      );
                                    }
                                  },
                                ),
                              );
                            }

                            if (state is BlocFailureState) {
                              return Expanded(
                                child: Column(
                                  children: [
                                    const Spacer(),
                                    Text(state.error.message),
                                    const Spacer(),
                                  ],
                                ),
                              );
                            }

                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class ProfileRecordingsUiModel {
  final List<RecordingModel> finalRecordings;
  final Map<String, List<RecordingModel>> draftRecordings;
  final bool showCurrentRecording;

  const ProfileRecordingsUiModel({
    required this.finalRecordings,
    required this.draftRecordings,
    required this.showCurrentRecording,
  });
}
